import React from 'react';
import { motion } from 'framer-motion';
import { Shield, Target, Eye, Award, Users } from 'lucide-react';

const About = () => {
  const leadership = [
    {
      name: "<PERSON> <PERSON><PERSON><PERSON>",
      title: "Chairman",
      image: "/assets/images/members/MAJ MD TOUFIKUL MUNEEM.jpg"
    },
    {
      name: "Brig Gen <PERSON>",
      title: "Managing Director",
      image: "/assets/images/members/BRIG GEN MD ZOBAIDUR RAHMAN.jpg"
    },
    {
      name: "Maj <PERSON>",
      title: "Board Member",
      image: "/assets/images/members/MG KAMRUL H KHAN.jpg"
    },
    {
      name: "<PERSON><PERSON>",
      title: "Board Member",
      image: "/assets/images/members/SHANARA BEGUM.jpg"
    },
    {
      name: "Brig Gen <PERSON>",
      title: "Board Member",
      image: "/assets/images/members/BG MD HABIBUR RAHMAN.jpg"
    },
    {
      name: "<PERSON> <PERSON><PERSON>",
      title: "Board Member",
      image: "/assets/images/members/DR ISTIAQUE ANWAR.jpg"
    },
    {
      name: "Prof Dr <PERSON>",
      title: "Board Member",
      image: "/assets/images/members/PROF A SAKUR KHAN.jpg"
    },
    {
      name: "Lt Col Md Emdadul Haque",
      title: "Board Member",
      image: "/assets/images/members/LT COL MD EMDADUL HAQUE.jpg"
    },
    {
      name: "Prof Dr Begum Nasrin",
      title: "Board Member",
      image: "/assets/images/members/PROF BEGUM NASRIN.jpg"
    },
    {
      name: "Lt Col SM Nazrul Islam",
      title: "Board Member",
      image: "/assets/images/members/LT COL SM NAZRUL ISLAM.jpg"
    },
    {
      name: "Mahfuzur R Chowdhury",
      title: "Board Member",
      image: "/assets/images/members/MAHFUZUR R CHOWDHURY.jpg"
    },
    {
      name: "Ipsha Nazia Adiba",
      title: "Board Member",
      image: "/assets/images/members/IPSHA NAZIA ADIBA.jpg"
    },
    {
      name: "Dr A.R.M Momtajuddin",
      title: "Board Member",
      image: "/assets/images/members/ARM MOMTAJUDDIN.jpg"
    },
    {
      name: "Prof Dr Md Kamruzzaman",
      title: "Board Member",
      image: "/assets/images/members/PROF MD KAMRUZZAMAN.jpg"
    },
    {
      name: "Dr AFM Zohurul Haque",
      title: "Board Member",
      image: "/assets/images/members/DR AFM ZOHURUL HAQUE.jpg"
    },
    {
      name: "Dr Sohel Akhter",
      title: "Board Member",
      image: "/assets/images/members/DR SOHEL AKHTER.jpg"
    },
    {
      name: "Maj Gen Md Mahbubur Rahman",
      title: "Board Member",
      image: "/assets/images/members/MG MD MAHBUBUR RAHMAN.jpg"
    },
    {
      name: "Md Selim Reza",
      title: "Board Member",
      image: "/assets/images/members/MD SELIM REZA.jpg"
    },
    {
      name: "Brig Gen Md Delwar Hussain",
      title: "Board Member",
      image: "/assets/images/members/BG MD DELWAR HOSSAIN.jpg"
    },
    {
      name: "Col Md Mostafizur Rahman",
      title: "Board Member",
      image: "/assets/images/members/COL MDMOSTAFIZUR RAHMAN.jpg"
    },
    {
      name: "Lt Col Maksuda Begum",
      title: "Board Member",
      image: "/assets/images/members/LT COL MAKSUDA BEGUM.jpg"
    },
    {
      name: "Dr Shahana Begum",
      title: "Board Member",
      image: "/assets/images/members/DR. SHAHANA BEGUM.jpg"
    },
    {
      name: "Engr Selima Nargis",
      title: "Board Member",
      image: "/assets/images/members/ENGR. SELIMA NARGIS.jpg"
    },
    {
      name: "Prof Md Amanullah",
      title: "Board Member",
      image: "/assets/images/members/PROF MD AMANULLAH.jpg"
    },
    {
      name: "Afrin Sultana",
      title: "Board Member",
      image: "/assets/images/members/AFRIN SULTANA.jpg"
    },
    {
      name: "Lt Col Md Billal Hossain",
      title: "Board Member",
      image: "/assets/images/members/LT COL MD BILLAL HOSSAIN.jpg"
    },
    {
      name: "Md Mafrul Haque",
      title: "Board Member",
      image: "/assets/images/members/MD MAFRUL HAQUE.jpg"
    },
    {
      name: "Md Zakir H Khan",
      title: "Board Member",
      image: "/assets/images/members/MD ZAKIR H KHAN.jpg"
    },
    {
      name: "Mosammat Halima Begum",
      title: "Board Member",
      image: "/assets/images/members/MOSAMMAT HALIMA BEGUM.jpg"
    },
    {
      name: "Most Sayeeda Akhter",
      title: "Board Member",
      image: "/assets/images/members/MOST. SAYEEDA AKHTER.jpg"
    },
    {
      name: "Dr Mosammat Dipa",
      title: "Board Member",
      image: "/assets/images/members/DR MOSAMMAT DIPA.jpg"
    },
    {
      name: "Fihor Esrar Eham",
      title: "Board Member",
      image: "/assets/images/members/FIHOR ESRAR EHAM.jpg"
    }
  ];

  const values = [
    {
      icon: Shield,
      title: "Military Precision",
      description: "We bring the discipline and attention to detail from military service to every project we undertake."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "Uncompromising quality in every aspect of our work, from design to delivery."
    },
    {
      icon: Users,
      title: "Trust",
      description: "Building long-term relationships with our clients based on transparency and reliability."
    },
    {
      icon: Target,
      title: "Innovation",
      description: "Embracing cutting-edge technology and modern design principles in all our developments."
    }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              About <span className="text-yellow-400">Soldiers Builders BD</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Military precision meets luxury living. We don't just build properties — we craft legacies.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Inspirational Quotes */}
      <section className="py-16 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
              Words That Inspire Us
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "The best investment on Earth is earth."
              </p>
              <p className="text-yellow-400 font-semibold">— Louis Glickman</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "Your home should tell the story of who you are"
              </p>
              <p className="text-yellow-400 font-semibold">— Nate Berkus</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "To be happy at home is the ultimate result of all ambition."
              </p>
              <p className="text-yellow-400 font-semibold">— Samuel Johnson</p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-gray-900 p-6 rounded-xl text-center"
            >
              <p className="text-lg text-gray-300 italic mb-4">
                "He is the happiest, be he king or peasant, who finds peace in his home."
              </p>
              <p className="text-yellow-400 font-semibold">— Johann Wolfgang von Goethe</p>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="mt-8 text-center"
          >
            <div className="bg-gray-900 p-6 rounded-xl max-w-4xl mx-auto">
              <p className="text-lg text-gray-300 italic mb-4">
                "Where we love is home; home that our feet may leave, but not our hearts."
              </p>
              <p className="text-yellow-400 font-semibold">— Oliver Wendell Holmes Sr.</p>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Our Story */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                Our Story
              </h2>
              <div className="space-y-6 text-gray-300 leading-relaxed">
                <p>
                  At Soldiers Builders BD, we don't just build properties — we craft legacies. Synonymous with
                  refined living, architectural excellence, and uncompromising quality, Soldiers Builders BD
                  stands at the forefront of luxury real estate development.
                </p>
                <p>
                  With an unwavering commitment to precision and prestige, our developments reflect elegance 
                  that transcends trends and endures through generations. Founded on the principles of 
                  integrity, innovation, and meticulous craftsmanship, Soldiers Builders BD is a symbol of
                  strength and sophistication.
                </p>
                <p>
                  Our name represents our discipline, our dedication to detail, and our relentless pursuit 
                  of excellence — values reminiscent of elite forces, now translated into the art of construction.
                </p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <img
                src="https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Luxury Building"
                className="w-full h-96 object-cover rounded-xl shadow-2xl"
              />
              <div className="absolute inset-0 bg-gradient-to-tr from-yellow-400/20 to-transparent rounded-xl"></div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex items-center mb-6">
                <div className="bg-yellow-400 p-3 rounded-lg mr-4">
                  <Target className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-3xl font-bold text-white">Our Mission</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                To deliver exceptional real estate experiences by combining visionary design, 
                high-end construction standards, and personalized service—ensuring every property 
                is not only a home but a statement. We are committed to exceeding expectations, 
                transforming dreams into reality, and creating spaces that resonate with elegance, 
                functionality, and enduring value.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex items-center mb-6">
                <div className="bg-yellow-400 p-3 rounded-lg mr-4">
                  <Eye className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-3xl font-bold text-white">Our Vision</h3>
              </div>
              <p className="text-gray-300 leading-relaxed">
                Shaping Tomorrow.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Our Values
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              The principles that guide every decision and shape every project
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-gray-800 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-300"
              >
                <div className="bg-yellow-400 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <value.icon className="h-8 w-8 text-gray-900" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-4">{value.title}</h3>
                <p className="text-gray-400">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Messages */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Leadership Messages
            </h2>
          </motion.div>

          <div className="space-y-16">
            {/* Chairman's Message */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex flex-col md:flex-row items-start gap-8">
                <div className="flex-shrink-0">
                  <img
                    src="https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=200"
                    alt="Major Toufikul Muneem"
                    className="w-32 h-32 rounded-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2">Chairman's Message</h3>
                  <h4 className="text-lg text-yellow-400 mb-4">Major Toufikul Muneem</h4>
                  <p className="text-gray-300 leading-relaxed">
                    "At Soldiers Builders BD, we do more than shape skylines, we shape futures. As someone who
                    once served in uniform and now serves in leadership, I've learned that true strength isn't
                    just built with steel and stone, but with purpose, integrity, and heart.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    Our journey began with a simple yet powerful vision: to redefine real estate as a deeply
                    human experience. We don't just construct buildings—we create spaces where families grow,
                    businesses thrive, and aspirations find a home. Every project is a promise, carried out
                    with care, conscience, and long-term commitment.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    We are honoured to build for those who value more than beauty, those who seek trust,
                    legacy, and a sense of belonging. At Soldiers Builders BD, luxury is not only in how a space
                    looks, but in how it feels, and what it stands for.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    In every design, there is vision. In every foundation, resilience. And behind every
                    achievement, a passion that drives us forward."
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Managing Director's Message */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 p-8 rounded-xl"
            >
              <div className="flex flex-col md:flex-row items-start gap-8">
                <div className="flex-shrink-0 md:order-2">
                  <img
                    src="https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=200"
                    alt="Brigadier General Md Zobaidur Rahman"
                    className="w-32 h-32 rounded-full object-cover"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-white mb-2">Managing Director's Message</h3>
                  <h4 className="text-lg text-yellow-400 mb-4">Brigadier General Md Zobaidur Rahman</h4>
                  <p className="text-gray-300 leading-relaxed">
                    "In a rapidly transforming Bangladesh, where skylines are rising and the rhythm of life
                    is quickening, the need for thoughtfully built, enduring spaces has never been more urgent.
                    As cities grow, so do the dreams of the people within them, and those dreams deserve more
                    than concrete and glass. They deserve commitment. They deserve precision. They deserve integrity.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    At Soldiers Builders BD, we bring a unique perspective - shaped by my years in uniform, where
                    every mission demanded clarity, accountability, and execution without error. These same
                    principles guide our approach to every project. Our discipline is not just military; it is
                    moral, architectural, and deeply human.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    We serve individuals, families, and businesses who want more than a property. They want
                    confidence in the ground beneath their feet. They want a team that listens, understands,
                    and delivers. We consider it an honor to be trusted with that responsibility.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    We do not simply develop land - we cultivate futures. We understand that every family,
                    every investor, and every community that turns to us is placing their hopes into our hands.
                    And we honor that trust with a devotion to excellence that runs deeper than blueprints and budgets.
                  </p>
                  <p className="text-gray-300 leading-relaxed mt-4">
                    Here, luxury is not just in the finish - it is in the philosophy. In the way we treat people.
                    In the legacy we leave behind. In every structure we build, there is strength. In every detail,
                    a promise. And behind every project - a team that leads with purpose."
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Leadership Team
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Meet the distinguished leaders who guide Soldiers Builders BD with military precision and vision
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {leadership.map((leader, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-gray-800 p-6 rounded-xl text-center hover:bg-gray-700 transition-all duration-300"
              >
                <img
                  src={leader.image}
                  alt={leader.name}
                  className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-lg font-semibold text-white mb-2">{leader.name}</h3>
                <p className="text-yellow-400">{leader.title}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Quote Section */}
      <section className="py-20 bg-gradient-to-r from-yellow-400 to-yellow-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              "The best investment on Earth is earth."
            </h2>
            <p className="text-xl text-gray-800">
              — Louis Glickman
            </p>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default About;