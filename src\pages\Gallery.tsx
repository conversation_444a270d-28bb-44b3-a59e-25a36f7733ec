import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Filter, ZoomIn } from 'lucide-react';

const Gallery = () => {
  const [filter, setFilter] = useState('all');

  const galleryImages = [
    {
      id: 1,
      category: 'exterior',
      title: "Harun's Nest - Modern Facade",
      image: '/assets/images/projects/haruns-nest/IMG-20250517-WA0013.jpg',
      description: 'Contemporary architectural design with premium materials'
    },
    {
      id: 2,
      category: 'interior',
      title: 'Luxury Living Room',
      image: '/assets/images/interior/luxury-living-room-2.jpg',
      description: 'Elegant interior design with premium finishes'
    },
    {
      id: 13,
      category: 'interior',
      title: 'Contemporary Living Space',
      image: '/assets/images/interior/Quiet-luxury-in-a-contemporary-living-room-by-Decorilla-1024x574.jpg',
      description: 'Modern contemporary living room with quiet luxury design'
    },
    {
      id: 14,
      category: 'interior',
      title: 'Elegant Dining Room',
      image: '/assets/images/interior/LiV-Din_001_View02.jpg',
      description: 'Sophisticated dining area with modern furnishing'
    },
    {
      id: 15,
      category: 'interior',
      title: 'Premium Kitchen Design',
      image: '/assets/images/interior/luxury-interior.jpg',
      description: 'State-of-the-art kitchen with luxury finishes'
    },
    {
      id: 16,
      category: 'interior',
      title: 'Luxury Fitness Center',
      image: '/assets/images/interior/luxury-gym-interior-design-3-1024x768.jpg',
      description: 'Premium gym facility with modern equipment'
    },
    {
      id: 17,
      category: 'interior',
      title: 'Hotel-Style Gym',
      image: '/assets/images/interior/luxury-hotel-gym-design-45707-11618401.jpg',
      description: 'Luxury hotel-style fitness center design'
    },
    {
      id: 18,
      category: 'interior',
      title: 'Luxury Living Tables',
      image: '/assets/images/interior/luxury-living-room-tables.jpg',
      description: 'Premium living room with designer furniture'
    },
    {
      id: 19,
      category: 'interior',
      title: 'Luxury Home Accessories',
      image: '/assets/images/interior/Luxury-home-accessories-Amelia-R.jpg',
      description: 'High-end home accessories and decor'
    },
    {
      id: 20,
      category: 'interior',
      title: 'Panoramic Luxury Interior',
      image: '/assets/images/interior/pngtree-stunning-3d-render-illustration-of-a-luxurious-interior-with-panoramic-windows-picture-image_5794998.jpg',
      description: 'Stunning luxury interior with panoramic windows'
    },
    {
      id: 21,
      category: 'interior',
      title: 'Luxury Apartment Living',
      image: '/assets/images/interior/luxury-apartment-24-3d-model-9bc90307fe.jpg',
      description: 'Modern luxury apartment interior design'
    },
    {
      id: 22,
      category: 'interior',
      title: 'Contemporary Family Room',
      image: '/assets/images/interior/Family-Room-1366x768.jpg',
      description: 'Spacious family room with contemporary design'
    },
    {
      id: 23,
      category: 'interior',
      title: 'Luxury Hall Design',
      image: '/assets/images/interior/L_U_X_U_R_Y_H_A_L_L_1715956191481_1715956321541.jpg',
      description: 'Grand luxury hall with premium finishes'
    },
    {
      id: 24,
      category: 'interior',
      title: 'Premium Living Room',
      image: '/assets/images/interior/PG---Living-Room.jpg',
      description: 'Elegant premium living room design'
    },
    {
      id: 25,
      category: 'interior',
      title: 'Luxury Home Interior',
      image: '/assets/images/interior/NPP-luxury-home-interiors.png',
      description: 'Comprehensive luxury home interior design'
    },
    {
      id: 3,
      category: 'exterior',
      title: 'Tilottoma - Grand Entrance',
      image: '/assets/images/projects/tilottoma/m-01_1 - Photo.jpg',
      description: 'Impressive entrance with landscaping'
    },
    {
      id: 26,
      category: 'interior',
      title: 'Beige and Green Luxury',
      image: '/assets/images/interior/A-Slice-Of-Luxury-In-Beige-And-Green-Interiors-1.jpg',
      description: 'Luxury interior with beige and green color scheme'
    },
    {
      id: 27,
      category: 'interior',
      title: 'Contemporary Penthouse',
      image: '/assets/images/interior/Contemporary-interior-decorations-breathe-life-into-this-penthouse-living-rooms-design..jpg',
      description: 'Contemporary penthouse living room design'
    },
    {
      id: 28,
      category: 'interior',
      title: 'Luxury Interior Trends',
      image: '/assets/images/interior/2022 Interior Design Trends for Luxury Homes Featured.jpg',
      description: '2022 luxury home interior design trends'
    },
    {
      id: 29,
      category: 'interior',
      title: 'Elegant Home Design',
      image: '/assets/images/interior/1677628080_en-idei-club-p-luxury-home-interior-pinterest-2.jpg',
      description: 'Pinterest-inspired luxury home interior'
    },
    {
      id: 30,
      category: 'interior',
      title: 'Modern Luxury Space',
      image: '/assets/images/interior/98e34181b741ce7cdc41bda8ff61c54a.jpg',
      description: 'Modern luxury interior space design'
    },
    {
      id: 31,
      category: 'interior',
      title: 'Premium Interior Design',
      image: '/assets/images/interior/39a9b03fe5f461d06a3a7d12905ad26d.jpg',
      description: 'Premium interior design with modern elements'
    },
    {
      id: 32,
      category: 'interior',
      title: 'Luxury Living Concept',
      image: '/assets/images/interior/26aac8e5f22b5160c2fed1f1a07f886f.jpg',
      description: 'Luxury living room concept design'
    },
    {
      id: 33,
      category: 'interior',
      title: 'Contemporary Home Interior',
      image: '/assets/images/interior/42c36c10-eca3-407f-8e7f-65a6c1f835e0.jpg',
      description: 'Contemporary home interior with modern styling'
    },
    {
      id: 34,
      category: 'interior',
      title: 'Luxury Home Design',
      image: '/assets/images/interior/6984_3672254_0_b.jpg',
      description: 'Comprehensive luxury home interior design'
    },
    {
      id: 5,
      category: 'construction',
      title: 'Mehnaz Project Progress',
      image: '/assets/images/projects/mehnaz/WhatsApp Image 2025-05-28 at 15.44.17_a52b477d.jpg',
      description: 'Quality construction with attention to detail'
    },
    {
      id: 35,
      category: 'interior',
      title: 'Luxury Interior Showcase',
      image: '/assets/images/interior/1480d95a-53e1-4fe3-af32-098c0bbd4da7.jpg',
      description: 'Luxury interior design showcase'
    },
    {
      id: 36,
      category: 'interior',
      title: 'Modern Home Interior',
      image: '/assets/images/interior/1719578420558.png',
      description: 'Modern home interior design concept'
    },
    {
      id: 37,
      category: 'interior',
      title: 'Luxury Living Space',
      image: '/assets/images/interior/1723200045.jpg',
      description: 'Spacious luxury living area design'
    },
    {
      id: 38,
      category: 'interior',
      title: 'Premium Interior Design',
      image: '/assets/images/interior/20.jpg',
      description: 'Premium interior design with elegant finishes'
    },
    {
      id: 39,
      category: 'interior',
      title: 'Elegant Interior Concept',
      image: '/assets/images/interior/e5e7a1cde3e2b2ac39f577a038e40f4c.jpeg',
      description: 'Elegant interior design concept'
    },
    {
      id: 40,
      category: 'interior',
      title: 'Luxury Home Living',
      image: '/assets/images/interior/gettyimages-1271519858-640x640.jpg',
      description: 'Luxury home living room design'
    },
    {
      id: 41,
      category: 'interior',
      title: 'Modern Home Design',
      image: '/assets/images/interior/home-663226_1280.jpg',
      description: 'Modern home interior design'
    },
    {
      id: 42,
      category: 'interior',
      title: 'Contemporary Living Room',
      image: '/assets/images/interior/hq720.jpg',
      description: 'Contemporary living room design'
    },
    {
      id: 43,
      category: 'interior',
      title: 'Luxury Interior Design',
      image: '/assets/images/interior/hq720 (1).jpg',
      description: 'High-quality luxury interior design'
    },
    {
      id: 44,
      category: 'interior',
      title: 'Premium Home Interior',
      image: '/assets/images/interior/hq720 (2).jpg',
      description: 'Premium home interior styling'
    },
    {
      id: 45,
      category: 'interior',
      title: 'Modern Luxury Living',
      image: '/assets/images/interior/hq720 (3).jpg',
      description: 'Modern luxury living space'
    },
    {
      id: 7,
      category: 'exterior',
      title: 'Bondhu Bilash Complex',
      image: '/assets/images/projects/bondhu-bilash/WhatsApp Image 2025-06-22 at 3.10.15 PM.jpeg',
      description: 'Complete residential complex'
    },
    {
      id: 46,
      category: 'interior',
      title: 'Luxury Interior Picture',
      image: '/assets/images/interior/picture1-20.jpg',
      description: 'Luxury interior design picture'
    },
    {
      id: 47,
      category: 'interior',
      title: 'Premium Render Design',
      image: '/assets/images/interior/render1 copy.jpg',
      description: 'Premium interior render design'
    },
    {
      id: 48,
      category: 'interior',
      title: 'Luxury Interior Thumb',
      image: '/assets/images/interior/thumb2022zpHqj1T77UR4.jpg',
      description: 'Luxury interior design thumbnail'
    },
    {
      id: 49,
      category: 'interior',
      title: 'Unnamed Luxury Design',
      image: '/assets/images/interior/unnamed.jpg',
      description: 'Luxury interior design concept'
    },
    {
      id: 50,
      category: 'interior',
      title: 'Custom Interior Design',
      image: '/assets/images/interior/Untitled design.jpg',
      description: 'Custom luxury interior design'
    },
    {
      id: 51,
      category: 'interior',
      title: 'Modern Interior Concept',
      image: '/assets/images/interior/1-1.png',
      description: 'Modern interior design concept'
    },
    {
      id: 9,
      category: 'construction',
      title: 'Tilottoma Construction',
      image: '/assets/images/projects/tilottoma/m-01_2 - Photo.jpg',
      description: 'Strong foundation construction'
    },
    {
      id: 10,
      category: 'handover',
      title: 'Chandrima Bilash Handover',
      image: '/assets/images/projects/chandrima-bilash/WhatsApp Image 2025-06-22 at 3.21.45 PM.jpeg',
      description: 'Happy families receiving their dream homes'
    },

    {
      id: 12,
      category: 'exterior',
      title: "Habib's Utopia Night View",
      image: '/assets/images/projects/habibs-utopia/WhatsApp Image 2025-06-23 at 3.14.29 PM.jpeg',
      description: 'Beautiful night illumination'
    }
  ];

  const filteredImages = filter === 'all' 
    ? galleryImages 
    : galleryImages.filter(image => image.category === filter);

  const categories = [
    { key: 'all', label: 'All Images' },
    { key: 'exterior', label: 'Exterior' },
    { key: 'interior', label: 'Interior' },
    { key: 'construction', label: 'Construction' },
    { key: 'handover', label: 'Handover' }
  ];

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              Project <span className="text-yellow-400">Gallery</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Explore our completed projects and ongoing developments through our comprehensive gallery
            </p>
          </motion.div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-gray-900 border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 sm:mb-0">
              <Filter className="h-5 w-5 text-yellow-400" />
              <span className="text-white font-medium">Filter Gallery:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.key}
                  onClick={() => setFilter(category.key)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    filter === category.key
                      ? 'bg-yellow-400 text-gray-900'
                      : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                  }`}
                >
                  {category.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredImages.map((image, index) => (
              <motion.div
                key={image.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                className="bg-gray-800 rounded-xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 group"
              >
                <div className="relative">
                  <img
                    src={image.image}
                    alt={image.title}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <motion.div
                      initial={{ scale: 0 }}
                      whileHover={{ scale: 1 }}
                      className="bg-yellow-400 p-3 rounded-full"
                    >
                      <ZoomIn className="h-6 w-6 text-gray-900" />
                    </motion.div>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="px-3 py-1 bg-yellow-400 text-gray-900 text-sm font-medium rounded-full capitalize">
                      {image.category}
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-2">{image.title}</h3>
                  <p className="text-gray-400">{image.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Video Section */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Virtual Tours
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              Experience our projects through immersive virtual tours
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8 text-center"
            >
              <div className="bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <ZoomIn className="h-8 w-8 text-gray-900" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">360° Virtual Tours</h3>
              <p className="text-gray-400 mb-6">
                Take a complete virtual walkthrough of our model apartments and experience 
                the luxury before you visit.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-200"
              >
                Start Virtual Tour
              </motion.button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8 text-center"
            >
              <div className="bg-yellow-400 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <ZoomIn className="h-8 w-8 text-gray-900" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Project Videos</h3>
              <p className="text-gray-400 mb-6">
                Watch detailed video presentations of our projects, construction progress, 
                and client testimonials.
              </p>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-yellow-400 text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-500 transition-all duration-200"
              >
                Watch Videos
              </motion.button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-yellow-400 to-yellow-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              See It in Person
            </h2>
            <p className="text-xl text-gray-800 max-w-2xl mx-auto">
              Nothing beats experiencing our quality and craftsmanship firsthand
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gray-900 text-yellow-400 px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-all duration-200"
              >
                Schedule Site Visit
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-gray-900 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-900 hover:text-yellow-400 transition-all duration-200"
              >
                Download Gallery
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Gallery;