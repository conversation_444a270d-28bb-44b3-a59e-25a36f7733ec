import React from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  MapPin, 
  Home, 
  Car, 
  Users, 
  Building, 
  Calendar,
  Phone,
  Download,
  CheckCircle
} from 'lucide-react';

const ProjectDetail = () => {
  const { projectId } = useParams();

  const projects = {
    'haruns-nest': {
      id: 1,
      name: "Harun's Nest",
      location: "Sector-16, Road# 502/E, Plot# 003, Jolshiri Abashon, Dhaka",
      image: "https://images.pexels.com/photos/280222/pexels-photo-280222.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Elegant south-facing single-row plot with dual access. Beside 140 ft boulevard, steps from a five-star hotel and top university.",
      detailedDescription: "Harun's Nest represents the pinnacle of luxury living in Jolshiri Abashon. This prestigious development offers an elegant south-facing single-row plot with dual access, strategically positioned beside a 140 ft boulevard. The location provides unparalleled convenience, being just steps away from a five-star hotel and a top university, making it perfect for both residential comfort and investment potential.",
      landSize: "5 Katha (3600 SFT)",
      buildingType: "G+M+8",
      apartments: 8,
      apartmentSize: "2850 SFT",
      bedrooms: 4,
      parking: 9,
      lift: "8 Passenger",
      facing: "South-West Corner Plot",
      status: "ongoing",
      features: ["Gym", "Office", "Meeting Space", "Rooftop Party Place"],
      amenities: [
        "24/7 Security & CCTV Surveillance",
        "Backup Generator",
        "Elevator with Emergency Features",
        "Rooftop Garden & Party Area",
        "Gymnasium & Fitness Center",
        "Business Center & Meeting Rooms",
        "Covered Parking",
        "Children's Play Area"
      ],
      gallery: [
        "https://images.pexels.com/photos/280222/pexels-photo-280222.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1080721/pexels-photo-1080721.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1743229/pexels-photo-1743229.jpeg?auto=compress&cs=tinysrgb&w=800"
      ]
    },
    'tilottoma': {
      id: 2,
      name: "Tilottoma",
      location: "Sector-12, Road# 506/E, Plot# 003, Jolshiri Abashon, Dhaka",
      image: "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Tranquil south-facing retreat near the lake. A serene, residential enclave of quiet sophistication.",
      detailedDescription: "Tilottoma offers a tranquil south-facing retreat near the pristine lake of Jolshiri Abashon. This serene residential enclave embodies quiet sophistication, providing residents with a peaceful living environment while maintaining easy access to urban conveniences. The proximity to the lake ensures fresh air, scenic views, and a connection with nature that's rare in urban developments.",
      landSize: "5 Katha (3600 SFT)",
      buildingType: "G+M+8",
      apartments: 8,
      apartmentSize: "2850 SFT",
      bedrooms: 4,
      parking: 10,
      lift: "8 Passenger",
      facing: "South Facing Plot",
      status: "ongoing",
      features: ["Lake View", "Peaceful Environment", "Premium Location"],
      amenities: [
        "Lake View Apartments",
        "Landscaped Gardens",
        "Walking Trails",
        "24/7 Security",
        "Backup Power Supply",
        "Modern Elevator System",
        "Covered Parking",
        "Community Hall"
      ],
      gallery: [
        "https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/2102587/pexels-photo-2102587.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1350789/pexels-photo-1350789.jpeg?auto=compress&cs=tinysrgb&w=800"
      ]
    },
    'chandra-neer': {
      id: 3,
      name: "Chandra Neer",
      location: "Sector-12, Road# 501, Plot# 017, Jolshiri Abashon, Dhaka",
      image: "https://images.pexels.com/photos/2581922/pexels-photo-2581922.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Grand north-east corner plot with triple road frontage. Direct access to 140 ft main road. Airy, open & full of light.",
      detailedDescription: "Chandra Neer stands as a testament to architectural excellence with its grand north-east corner plot featuring triple road frontage. This prestigious development offers direct access to the 140 ft main road, ensuring excellent connectivity and accessibility. The strategic positioning allows for maximum natural light and ventilation, creating airy, open living spaces that epitomize modern luxury.",
      landSize: "5 Katha (3600 SFT)",
      buildingType: "G+M+8",
      apartments: 8,
      apartmentSize: "2850 SFT",
      bedrooms: 4,
      parking: 9,
      lift: "9 Passenger",
      facing: "North East Corner Plot",
      status: "ongoing",
      features: ["Triple Road Frontage", "140 ft Main Road", "Premium Corner"],
      amenities: [
        "Triple Road Access",
        "Premium Corner Location",
        "Maximum Natural Light",
        "Excellent Ventilation",
        "24/7 Security System",
        "Modern Elevator",
        "Covered Parking",
        "Rooftop Facilities"
      ],
      gallery: [
        "https://images.pexels.com/photos/2581922/pexels-photo-2581922.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/323780/pexels-photo-323780.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1080721/pexels-photo-1080721.jpeg?auto=compress&cs=tinysrgb&w=800"
      ]
    },
    'bondhu-bilash': {
      id: 4,
      name: "Bondhu Bilash",
      location: "BLDGN # 15, Road # Avenue 2, Block # C, Chondrima Model Town, Mohammadpur, Dhaka",
      image: "https://images.pexels.com/photos/2102587/pexels-photo-2102587.jpeg?auto=compress&cs=tinysrgb&w=800",
      description: "Completed luxury residential project with modern amenities and premium finishes.",
      detailedDescription: "Bondhu Bilash represents our commitment to excellence in completed luxury residential developments. This prestigious project in Chondrima Model Town showcases our ability to deliver on time with uncompromising quality. Every detail has been meticulously crafted to provide residents with a lifestyle that combines comfort, luxury, and convenience.",
      landSize: "5 Katha (3600 SFT)",
      buildingType: "G+9",
      apartments: 8,
      apartmentSize: "2850 SFT",
      bedrooms: 4,
      parking: 10,
      lift: "2x10 Passenger",
      facing: "South Corner Plot",
      status: "completed",
      features: ["Dual Lift", "Premium Finishes", "Completed"],
      amenities: [
        "Dual Elevator System",
        "Premium Interior Finishes",
        "24/7 Security & CCTV",
        "Backup Generator",
        "Covered Parking",
        "Landscaped Common Areas",
        "Modern Fire Safety System",
        "High-Speed Internet Ready"
      ],
      gallery: [
        "https://images.pexels.com/photos/2102587/pexels-photo-2102587.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1571460/pexels-photo-1571460.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1080721/pexels-photo-1080721.jpeg?auto=compress&cs=tinysrgb&w=800",
        "https://images.pexels.com/photos/1743229/pexels-photo-1743229.jpeg?auto=compress&cs=tinysrgb&w=800"
      ]
    }
  };

  const project = projects[projectId as keyof typeof projects];

  if (!project) {
    return (
      <div className="min-h-screen pt-16 bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-4">Project Not Found</h1>
          <Link to="/projects" className="text-yellow-400 hover:text-yellow-300">
            Return to Projects
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-16">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-gray-900 to-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <Link 
              to="/projects"
              className="inline-flex items-center text-yellow-400 hover:text-yellow-300 mb-6 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Projects
            </Link>
            <div className="flex items-center mb-4">
              <h1 className="text-4xl md:text-6xl font-bold text-white mr-4">
                {project.name}
              </h1>
              <span className={`px-4 py-2 rounded-full text-sm font-medium ${
                project.status === 'completed' 
                  ? 'bg-green-600 text-white' 
                  : 'bg-yellow-600 text-white'
              }`}>
                {project.status === 'completed' ? 'Completed' : 'Ongoing'}
              </span>
            </div>
            <div className="flex items-start space-x-2 mb-6">
              <MapPin className="h-6 w-6 text-yellow-400 mt-1 flex-shrink-0" />
              <p className="text-xl text-gray-300">{project.location}</p>
            </div>
            <p className="text-lg text-gray-400 max-w-4xl">
              {project.detailedDescription}
            </p>
          </motion.div>
        </div>
      </section>

      {/* Project Gallery */}
      <section className="py-16 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {project.gallery.map((image, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative group"
              >
                <img
                  src={image}
                  alt={`${project.name} - Image ${index + 1}`}
                  className="w-full h-64 object-cover rounded-xl shadow-lg group-hover:shadow-2xl transition-shadow duration-300"
                />
                <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Project Specifications */}
      <section className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Specifications */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8"
            >
              <h2 className="text-3xl font-bold text-white mb-8">Project Specifications</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <Building className="h-6 w-6 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">{project.landSize}</div>
                    <div className="text-gray-400 text-sm">Land Size</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Building className="h-6 w-6 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">{project.buildingType}</div>
                    <div className="text-gray-400 text-sm">Building Type</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Home className="h-6 w-6 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">{project.apartmentSize}</div>
                    <div className="text-gray-400 text-sm">Apartment Size</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Users className="h-6 w-6 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">{project.bedrooms}</div>
                    <div className="text-gray-400 text-sm">Bedrooms</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Car className="h-6 w-6 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">{project.parking}</div>
                    <div className="text-gray-400 text-sm">Parking Spaces</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Building className="h-6 w-6 text-yellow-400" />
                  <div>
                    <div className="text-white font-medium">{project.apartments}</div>
                    <div className="text-gray-400 text-sm">Total Units</div>
                  </div>
                </div>
              </div>
              
              <div className="mt-8">
                <h3 className="text-xl font-semibold text-white mb-4">Plot Facing</h3>
                <div className="bg-yellow-400 text-gray-900 px-4 py-2 rounded-lg inline-block font-medium">
                  {project.facing}
                </div>
              </div>
            </motion.div>

            {/* Amenities */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              className="bg-gray-900 rounded-xl p-8"
            >
              <h2 className="text-3xl font-bold text-white mb-8">Amenities & Features</h2>
              <div className="space-y-4">
                {project.amenities.map((amenity, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-yellow-400 flex-shrink-0" />
                    <span className="text-gray-300">{amenity}</span>
                  </div>
                ))}
              </div>

              <div className="mt-8">
                <h3 className="text-xl font-semibold text-white mb-4">Special Features</h3>
                <div className="flex flex-wrap gap-2">
                  {project.features.map((feature, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-sm"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-yellow-400 to-yellow-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
              Interested in {project.name}?
            </h2>
            <p className="text-xl text-gray-800 max-w-2xl mx-auto">
              {project.status === 'completed' 
                ? 'Contact us for similar upcoming projects or resale opportunities'
                : 'Book your apartment today and secure your future'
              }
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-8">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gray-900 text-yellow-400 px-8 py-3 rounded-lg font-semibold hover:bg-gray-800 transition-all duration-200 flex items-center space-x-2"
              >
                <Phone className="h-5 w-5" />
                <span>Call Now</span>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-gray-900 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-900 hover:text-yellow-400 transition-all duration-200 flex items-center space-x-2"
              >
                <Calendar className="h-5 w-5" />
                <span>Schedule Visit</span>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-gray-900 text-gray-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-900 hover:text-yellow-400 transition-all duration-200 flex items-center space-x-2"
              >
                <Download className="h-5 w-5" />
                <span>Download Brochure</span>
              </motion.button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProjectDetail;